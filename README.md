# 🤖 RAG智能问答系统

一个基于检索增强生成(RAG)技术的智能问答系统，支持多种文档格式的上传和智能问答，包含内容增强和数据导出功能。

## ✨ 功能特点

- 📁 **多格式支持**: 支持TXT、PDF、DOCX、MD、XLSX、PPTX等文档格式
- 🤖 **智能问答**: 基于上传文档内容的智能问答，已优化超时处理
- 📚 **知识库管理**: 支持多个知识库的创建和管理
- 🔄 **问答数据生成**: 自动从文档生成问答对
- ✨ **内容增强**: AI驱动的文本分析、摘要生成和标签提取
- 💾 **数据导出**: 多格式导出(JSON/CSV/Excel)和系统备份功能
- 🌐 **Web界面**: 简洁易用的Web操作界面

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 依赖包：pandas, numpy, gradio, jieba, zhipuai, sentence-transformers

### 安装依赖
```bash
pip install pandas numpy gradio jieba zhipuai sentence-transformers openpyxl python-docx PyPDF2
```

### 启动系统
```bash
python web_api.py
```

### 访问系统
- 地址：http://localhost:9999
- 用户名：user
- 密码：123

## 📖 使用说明

### 1. 知识库管理
- 上传文档文件夹创建新知识库
- 查看已有知识库内容
- 支持多种文档格式混合上传

### 2. 生成问答数据
- 选择知识库
- 点击"生成问答QA数据"
- 系统自动从文档内容生成问答对

### 3. RAG智能问答
- 选择知识库
- 输入问题
- 获取基于知识库内容的智能回答

### 4. 内容增强 ✨
- **智能摘要**: 输入文本，AI自动生成简洁摘要
- **内容分析**: 分析文本的可读性、复杂度、情感倾向等
- **自动标签**: 基于内容自动生成相关标签
- **质量评估**: 综合评估内容质量

### 5. 数据导出 💾
- **多格式导出**: 支持JSON、CSV、Excel格式导出知识库
- **系统备份**: 创建完整的系统备份文件
- **导出统计**: 查看导出和备份的统计信息
- **自动清理**: 自动清理旧备份文件

## 📁 文件结构

```
ai_rag_demo/
├── web_api.py              # 主Web界面
├── database.py             # 数据库操作
├── local_llm_model.py      # LLM调用模块（已优化）
├── document_processors.py  # 文档处理器
├── knowledge_extract.py    # 知识提取
├── content_enhancer.py     # 内容增强模块 ✨
├── data_export.py          # 数据导出模块 💾
├── emb_model.py           # 嵌入模型
├── load_documents.py      # 文档加载
├── moka-ai_m3e-base/      # 嵌入模型文件
└── data/                  # 数据目录
    ├── database_dir/      # 知识库文件
    ├── exports/          # 导出文件 💾
    └── backups/          # 备份文件 💾
```

## 🔧 配置说明

### API配置
- 系统使用智谱AI的GLM-4模型
- 需要在`local_llm_model.py`中配置API密钥

### 端口配置
- 默认端口：9999
- 可在`web_api.py`中修改`server_port`参数

### 导出配置
- 导出目录：`data/exports/`
- 备份目录：`data/backups/`
- 自动清理：保留最近10个备份

## 💡 使用技巧

1. **文档上传**: 建议将相关文档放在同一文件夹中一起上传
2. **问答优化**: 上传文档后先生成问答数据，可提高检索效果
3. **问题表述**: 使用具体、明确的问题获得更好的答案
4. **内容增强**: 可用于分析任何文本内容，不限于知识库文档
5. **数据备份**: 定期使用备份功能保护重要数据

## 🆕 新功能亮点

### 内容增强功能
- **智能摘要**: 基于LLM的高质量文本摘要
- **多维分析**: 可读性、复杂度、情感倾向分析
- **自动标签**: 智能提取关键词作为标签
- **质量评分**: 综合评估内容质量

### 数据导出功能
- **多格式支持**: JSON、CSV、Excel三种格式
- **完整备份**: 压缩备份整个系统数据
- **统计信息**: 详细的导出和备份统计
- **自动管理**: 自动清理旧备份，节省空间

## 🐛 故障排除

### 常见问题

1. **启动失败**
   - 检查Python版本（推荐3.8+）
   - 确认所有依赖已安装

2. **RAG问答超时**
   - 系统已优化超时处理
   - 会自动使用文档内容直接回答

3. **内容增强失败**
   - 检查输入文本是否为空
   - 确认LLM API可用

4. **导出失败**
   - 检查导出目录权限
   - 确认磁盘空间充足

## 📄 许可证

本项目开源，供学习和研究使用。

---

🎉 **体验强大的AI RAG智能问答和内容增强功能！**
