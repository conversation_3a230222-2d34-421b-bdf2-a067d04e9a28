# RAG智能问答系统 - 前端页面功能说明文档

## 系统概述

RAG智能问答系统采用Gradio框架构建现代化Web界面，提供直观易用的用户交互体验。系统包含5个主要功能页面，支持知识库管理、智能问答、内容分析等核心功能。

**访问信息：**
- 访问地址：http://localhost:9999
- 用户名：user
- 密码：123

---

## 页面结构总览

### 系统头部
- **主标题**：企业知识库管理系统
- **副标题**：基于AI技术的智能文档管理与问答平台
- **系统概览**：显示当前知识库数量统计
- **支持格式**：展示支持的文档格式标签（PDF、Word、Excel、PPT、TXT、Markdown）

---

## 📚 知识库管理页面

### 功能概述
知识库管理是系统的核心功能，负责文档上传、知识库创建、内容预览和知识库删除等操作。

### 页面布局

#### 左侧区域 - 知识库列表
- **知识库选择下拉框**
  - 显示所有已创建的知识库名称
  - 支持切换查看不同知识库
  - 实时更新知识库列表

- **知识库内容预览表格**
  - 以表格形式展示选中知识库的文档内容
  - 列名：文档内容
  - 支持滚动查看长文本内容
  - 实时响应知识库切换

#### 右侧区域 - 知识库操作

##### 新建知识库区域
- **功能说明卡片**
  - 操作说明：支持批量上传多种格式文档
  - 特性介绍：系统自动提取关键词命名、支持文件夹结构上传
- **文档上传按钮**
  - 标签：📁 选择文档文件夹
  - 支持文件夹批量上传
  - 自动识别支持的文档格式

##### 删除知识库区域
- **警告提示卡片**
  - ⚠️ 安全警告：删除操作不可恢复
  - 风险说明：将删除所有相关文件和数据
- **删除操作控件**
  - 知识库选择下拉框：选择要删除的知识库
  - 确认删除按钮：🗑️ 确认删除（红色警告样式）
  - 删除结果显示框：显示删除操作的结果信息

### 交互逻辑
1. **上传流程**：选择文件夹 → 格式检查 → 文本提取 → 关键词分析 → 知识库创建
2. **切换查看**：选择知识库 → 加载内容 → 更新预览表格
3. **删除流程**：选择知识库 → 确认删除 → 清理文件 → 更新列表

---

## 🔄 训练数据生成页面

### 功能概述
基于已创建的知识库文档，自动生成高质量的问答对数据，用于提升检索准确性和回答质量。

### 页面布局

#### 左侧区域 - 功能说明
- **功能介绍卡片**
  - 功能说明：基于知识库文档自动生成高质量问答对
  - 应用场景：
    - 提升检索准确性
    - 优化回答质量
    - 扩充训练数据集

#### 中间区域 - 操作控制
- **知识库选择下拉框**
  - 选择要生成问答数据的知识库
  - 显示所有可用知识库
- **生成按钮**
  - 标签：🚀 开始生成训练数据
  - 主要操作按钮样式

#### 右侧区域 - 结果展示
- **问答数据预览表格**
  - 列名：question（问题）、answer（答案）
  - 实时显示生成的问答对
  - 支持滚动查看所有数据

### 生成流程
1. 选择知识库 → 提取文档内容 → 调用GLM-4生成问答对 → 格式化数据 → 创建向量索引

---

## 💬 智能问答页面

### 功能概述
系统的核心应用功能，基于RAG技术提供智能问答服务，支持自然语言查询和上下文理解。

### 页面布局

#### 左侧区域 - 使用指南
- **使用指南卡片**
  - 支持的问题类型：
    - 产品功能咨询
    - 技术文档查询
    - 流程规范说明
    - 政策制度解读
  - 使用提示：问题描述越具体，回答越准确

- **知识库选择下拉框**
  - 选择相关的知识库进行问答
  - 支持切换不同领域的知识库

#### 右侧区域 - 问答交互
- **问题输入框**
  - 标签：请输入您的问题
  - 示例提示：公司的休假政策是什么？产品X的技术规格如何？
  - 支持多行文本输入（3行）

- **智能问答按钮**
  - 标签：🔍 智能问答
  - 主要操作按钮样式

- **AI回答显示框**
  - 标签：AI助手回答
  - 显示GLM-4生成的智能回答
  - 支持多行显示（6行）

#### 底部区域 - 检索结果
- **相关文档片段表格**
  - 显示向量检索到的相关文档内容
  - 提供回答的依据和来源
  - 支持查看检索的相似度和匹配程度

### 问答流程
1. 输入问题 → 选择知识库 → 向量检索 → 构建上下文 → GLM-4生成答案 → 显示结果

---

## 📄 内容分析页面

### 功能概述
提供AI驱动的文本智能分析功能，包括内容摘要、质量分析、自动标签生成等高级功能。

### 页面布局

#### 左侧区域 - 输入与说明
- **分析功能介绍卡片**
  - 分析功能：
    - 智能摘要：提取核心要点
    - 内容分析：情感、主题、关键词
    - 自动标签：生成分类标签
  - 适用场景：文档预处理、内容分类、质量评估

- **文本输入框**
  - 标签：输入文本内容
  - 支持大文本输入（8行）
  - 提示：支持中英文混合文本

- **分析按钮**
  - 标签：🔍 开始分析
  - 主要操作按钮样式

#### 右侧区域 - 分析结果
- **智能摘要显示框**
  - 标签：智能摘要
  - 显示AI生成的内容摘要（4行）
  - 提取文本核心要点

- **自动标签显示框**
  - 标签：自动标签
  - 显示系统生成的分类标签
  - 以逗号分隔的标签列表

- **详细分析结果**
  - 标签：详细分析结果
  - JSON格式显示分析数据
  - 包含情感分析、质量评分等指标

### 分析流程
1. 输入文本 → 调用分析引擎 → 生成摘要 → 提取标签 → 质量评估 → 显示结果

---

## 💾 数据管理页面

### 功能概述
提供数据导出和系统备份功能，支持多种格式的数据导出和完整的系统备份管理。

### 页面布局

#### 左侧区域 - 数据导出
- **数据导出功能卡片**
  - 支持格式：
    - JSON：完整结构化数据
    - CSV：表格数据分析
    - Excel：业务报表格式

- **导出操作控件**
  - 知识库选择下拉框：选择要导出的知识库
  - 导出格式选择：json/csv/excel三种格式
  - 导出按钮：📤 导出数据
  - 导出状态显示框：显示导出操作结果

#### 右侧区域 - 系统管理
- **系统管理功能卡片**
  - 管理功能：
    - 自动备份：定期数据备份
    - 统计分析：使用情况统计
    - 系统监控：运行状态监控

- **管理操作控件**
  - 备份按钮：🔄 创建系统备份
  - 备份状态显示框：显示备份操作结果
  - 统计按钮：📊 查看统计
  - 统计信息显示：JSON格式的系统统计数据

### 管理流程
1. **数据导出**：选择知识库 → 选择格式 → 收集数据 → 格式转换 → 保存文件
2. **系统备份**：收集文件 → 创建压缩包 → 清理旧备份 → 保存备份
3. **统计查看**：收集系统信息 → 格式化数据 → 显示统计结果

---

## 页面底部

### 系统信息
- **版权信息**：企业知识库管理系统 | 基于RAG技术的智能文档管理平台
- **功能特色**：支持多种文档格式 • 智能问答 • 数据分析 • 安全可靠

---

## 用户体验特性

### 界面设计
- **现代化风格**：采用渐变色彩和卡片式布局
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：清晰的功能分区和操作流程

### 交互体验
- **实时反馈**：操作结果即时显示
- **状态提示**：详细的操作状态和错误信息
- **数据同步**：多页面数据实时同步更新

### 安全特性
- **用户认证**：登录验证保护系统安全
- **操作确认**：危险操作需要二次确认
- **数据备份**：完善的数据备份和恢复机制
