import os
import time
import gradio as gr
import shutil
import jieba.analyse as aly
from collections import Counter
from database import MyDataBase
import pandas as pd
from database import load_database
from local_llm_model import get_ans
from knowledge_extract import generate_qa
from document_processors import DocumentProcessor, get_supported_formats, get_format_description
from content_enhancer import ContentEnhancer
from data_export import DataExportManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化功能模块
content_enhancer = ContentEnhancer()
export_manager = DataExportManager()


def get_type_name(files):
    """从文件内容中提取主题关键词作为知识库名称"""
    content = []
    processor = DocumentProcessor()

    for file in files:
        try:
            if processor.is_supported(file.name):
                # 使用文档处理器提取文本
                text = processor.extract_text(file.name)
                if text.strip():
                    # 取前1000个字符进行关键词提取
                    sample_text = text[:1000]
                    keywords = aly.tfidf(sample_text, topK=10)
                    content.extend(keywords)
            else:
                # 对于不支持的格式，尝试按文本文件读取
                try:
                    with open(file.name, encoding="utf-8") as f:
                        data = f.readlines(1)
                        if data:
                            content.extend(aly.tfidf(data[0]))
                except:
                    continue
        except Exception as e:
            logger.warning(f"提取关键词失败 {file.name}: {e}")
            continue

    if content:
        count = Counter(content)
        kw = count.most_common(2)
        return "".join([i[0] for i in kw])
    else:
        # 如果无法提取关键词，使用默认名称
        return "新知识库"


def upload(files):
    """上传并处理多种格式的文档文件"""
    global database_list, database_namelist

    processor = DocumentProcessor()
    supported_formats = get_supported_formats()

    # 检查是否有支持的文件格式
    valid_files = []
    for file in files:
        if processor.is_supported(file.name):
            valid_files.append(file)

    if not valid_files:
        supported_list = ", ".join(supported_formats)
        raise Exception(f"请上传支持的文档格式文件。支持的格式: {supported_list}")

    logger.info(f"找到 {len(valid_files)} 个支持的文件")

    type_name = get_type_name(valid_files)
    save_path = os.path.join("data/database_dir", type_name)

    if not os.path.exists(save_path):
        os.makedirs(save_path)
        # 同时创建txt和qa目录
        os.makedirs(os.path.join(save_path, "txt"))
        os.makedirs(os.path.join(save_path, "qa"))

    # 复制所有支持的文件到txt目录
    for file in valid_files:
        try:
            shutil.copy(file.name, os.path.join(save_path, "txt"))
            logger.info(f"已复制文件: {file.name}")
        except Exception as e:
            logger.error(f"复制文件失败 {file.name}: {e}")
            continue

    database = MyDataBase(save_path, type_name)
    database_list.append(database)
    database_namelist.append(type_name)

    context = pd.DataFrame(database.document.contents, columns=["context"])

    # 返回更新后的选择列表，让Gradio重新渲染dropdown
    updated_choices = database_namelist
    return (
        gr.update(choices=updated_choices, value=type_name),  # 知识库管理页面的dropdown
        context,  # 上下文数据
        gr.update(choices=updated_choices, value=type_name),  # 生成问答数据页面的dropdown
        gr.update(choices=updated_choices, value=type_name),  # RAG问答页面的dropdown
        gr.update(choices=updated_choices, value=type_name)   # 数据导出页面的dropdown
    )


def database_change(name):
    global database_list, database_namelist
    if name and name in database_namelist:
        context = pd.DataFrame(database_list[database_namelist.index(name)].document.contents, columns=["文档内容"])
        return context
    else:
        return pd.DataFrame()


def delete_knowledge_base(kb_name):
    """删除知识库"""
    global database_list, database_namelist

    try:
        current_count_html = f"""
        <div class="feature-card">
            <h4>当前知识库数量</h4>
            <h2 style="color: #667eea;">{len(database_namelist)}</h2>
        </div>
        """

        if not kb_name:
            return (
                "请选择要删除的知识库",
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                pd.DataFrame(),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=None),
                current_count_html
            )

        if kb_name not in database_namelist:
            return (
                "选择的知识库不存在",
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                pd.DataFrame(),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
                gr.update(choices=database_namelist, value=None),
                current_count_html
            )

        # 获取知识库索引
        kb_index = database_namelist.index(kb_name)

        # 删除文件系统中的知识库文件夹
        kb_path = os.path.join("data/database_dir", kb_name)
        if os.path.exists(kb_path):
            shutil.rmtree(kb_path)
            logger.info(f"已删除知识库文件夹: {kb_path}")

        # 删除缓存文件
        cache_files = [
            f".cache/{kb_name}_contents.pkl",
            f".cache/{kb_name}_faiss_index.pkl",
            f".cache/{kb_name}_faiss_index_df.pkl"
        ]

        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                logger.info(f"已删除缓存文件: {cache_file}")

        # 从内存中删除知识库
        database_list.pop(kb_index)
        database_namelist.pop(kb_index)

        # 更新dropdown选择
        updated_choices = database_namelist.copy()  # 使用副本确保更新
        new_value = updated_choices[0] if updated_choices else None

        # 更新上下文显示
        if updated_choices and database_list:
            new_context = pd.DataFrame(database_list[0].document.contents, columns=["文档内容"])
        else:
            new_context = pd.DataFrame(columns=["文档内容"])

        success_msg = f"知识库 '{kb_name}' 已成功删除，当前剩余 {len(updated_choices)} 个知识库"
        logger.info(success_msg)

        # 更新知识库数量显示
        updated_count_html = f"""
        <div class="feature-card">
            <h4>当前知识库数量</h4>
            <h2 style="color: #667eea;">{len(updated_choices)}</h2>
        </div>
        """

        return (
            success_msg,
            gr.update(choices=updated_choices, value=new_value),  # 知识库管理页面的dropdown
            new_context,  # 上下文数据
            gr.update(choices=updated_choices, value=new_value),  # 生成问答数据页面的dropdown
            gr.update(choices=updated_choices, value=new_value),  # RAG问答页面的dropdown
            gr.update(choices=updated_choices, value=new_value),  # 数据导出页面的dropdown
            gr.update(choices=updated_choices, value=None),       # 删除页面的dropdown，重置为None
            updated_count_html                                    # 更新知识库数量显示
        )

    except Exception as e:
        error_msg = f"删除知识库失败: {str(e)}"
        logger.error(error_msg)

        error_count_html = f"""
        <div class="feature-card">
            <h4>当前知识库数量</h4>
            <h2 style="color: #667eea;">{len(database_namelist)}</h2>
        </div>
        """

        return (
            error_msg,
            gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
            pd.DataFrame(),
            gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
            gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
            gr.update(choices=database_namelist, value=database_namelist[0] if database_namelist else None),
            gr.update(choices=database_namelist, value=None),
            error_count_html
        )


def generate_rag_result(knowledge_name, question):
    """RAG智能问答"""
    try:
        start_time = time.time()

        # 输入验证
        if not knowledge_name or not question:
            return "请选择知识库并输入问题", pd.DataFrame()

        if knowledge_name not in database_namelist:
            return "选择的知识库不存在", pd.DataFrame()

        # 使用传统的RAG方法
        database = database_list[database_namelist.index(knowledge_name)]
        search_result = database.search(question, 3)

        # 如果没有找到问答数据，尝试使用文档内容直接回答
        if search_result.empty or len(search_result["answer"]) == 0:
            logger.info("没有找到问答数据，尝试使用文档内容")
            if database.document.contents:
                # 使用文档内容的前1000字符
                content = database.document.contents[0][:1000]
                prompt = f"基于以下文档内容回答问题：\n内容：{content}\n问题：{question}\n请简洁明了地回答"
                result = get_ans(prompt)

                # 创建一个简单的搜索结果DataFrame
                simple_result = pd.DataFrame({
                    "question": [question],
                    "answer": [content[:200] + "..."]
                })

                response_time = time.time() - start_time
                logger.info(f"使用文档内容回答，响应时间: {response_time:.2f}秒")

                return result, simple_result
            else:
                return "知识库中没有相关内容", pd.DataFrame()

        abstract = "\n".join(search_result["answer"])
        if not abstract.strip():
            return "检索到的内容为空，请尝试其他问题", pd.DataFrame()

        # 优化提示词，减少超时风险
        prompt = f"基于以下内容回答问题，要求简洁明了：\n内容：{abstract[:1000]}\n问题：{question}\n如果内容无法回答问题，请回复：不知道"

        logger.info(f"开始调用LLM，问题长度: {len(question)}, 上下文长度: {len(abstract)}")
        result = get_ans(prompt)

        # 记录响应时间
        response_time = time.time() - start_time
        logger.info(f"查询响应时间: {response_time:.2f}秒")

        return result, search_result

    except Exception as e:
        logger.error(f"RAG查询失败: {e}")
        return f"抱歉，处理您的问题时出现错误: {str(e)}", pd.DataFrame()


def process_qa_data(input_qa):
    global database_list, database_namelist
    try:
        select_database = database_list[database_namelist.index(input_qa)]
        contexts = select_database.document.contents

        if not contexts:
            # 如果没有文档内容，返回空的DataFrame
            empty_df = pd.DataFrame(columns=["question", "answer"])
            return empty_df

        # 生成问答对
        qa_df = generate_qa(input_qa, contexts)

        # 检查生成的问答对是否有效
        if qa_df is None or qa_df.empty:
            # 如果生成失败，创建一些基础的问答对
            qa_df = pd.DataFrame({
                "question": [f"关于{input_qa}的基本信息", f"{input_qa}是什么", f"{input_qa}的特点"],
                "answer": [contexts[0][:300] if contexts else "暂无内容"] * 3
            })

        # 确保DataFrame包含必要的列
        if "question" not in qa_df.columns or "answer" not in qa_df.columns:
            qa_df = pd.DataFrame({
                "question": [f"关于{input_qa}的基本信息"],
                "answer": [contexts[0][:300] if contexts else "暂无内容"]
            })

        # 创建嵌入数据库
        select_database.create_emb_database(qa_df)
        return qa_df

    except Exception as e:
        print(f"生成问答数据时出错: {e}")
        # 返回错误信息的DataFrame
        error_df = pd.DataFrame({
            "question": ["错误"],
            "answer": [f"生成问答数据时出错: {str(e)}"]
        })
        return error_df


# 全局变量 - 在模块级别初始化，以便其他模块可以导入
database_list, database_namelist = load_database()

if __name__ == "__main__":
    print("----------start-----------")

    # 获取支持格式的描述
    format_info = get_format_description()
    print(format_info)

    # 自定义CSS样式
    custom_css = """
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        color: white;
        text-align: center;
    }
    .feature-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
    }
    .tab-content {
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    """

    with gr.Blocks(title="企业知识库管理系统", css=custom_css) as demo:
        # 系统头部
        with gr.Row():
            gr.HTML("""
            <div class="main-header">
                <h1>企业知识库管理系统</h1>
                <p>基于AI技术的智能文档管理与问答平台</p>
            </div>
            """)

        # 快速操作面板
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 系统概览")
                kb_count_display = gr.HTML(f"""
                <div class="feature-card">
                    <h4>当前知识库数量</h4>
                    <h2 style="color: #667eea;">{len(database_namelist)}</h2>
                </div>
                """)
            with gr.Column(scale=2):
                gr.Markdown("### 支持格式")
                gr.HTML("""
                <div class="feature-card">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <span style="background: #e3f2fd; padding: 5px 10px; border-radius: 15px;">PDF</span>
                        <span style="background: #e8f5e8; padding: 5px 10px; border-radius: 15px;">Word</span>
                        <span style="background: #fff3e0; padding: 5px 10px; border-radius: 15px;">Excel</span>
                        <span style="background: #fce4ec; padding: 5px 10px; border-radius: 15px;">PPT</span>
                        <span style="background: #f3e5f5; padding: 5px 10px; border-radius: 15px;">TXT</span>
                        <span style="background: #e0f2f1; padding: 5px 10px; border-radius: 15px;">Markdown</span>
                    </div>
                </div>
                """)

        with gr.Tab("📚 知识库管理"):
            with gr.Row():
                with gr.Column(scale=2):
                    gr.Markdown("### 知识库列表")
                    knowledge_names = gr.Dropdown(
                        choices=database_namelist,
                        label="选择知识库",
                        value=database_namelist[0] if database_namelist else None,
                        info="选择要查看的知识库"
                    )
                    context = gr.DataFrame(
                        pd.DataFrame(database_list[0].document.contents, columns=["文档内容"])
                        if database_list else pd.DataFrame(),
                        label="知识库内容预览"
                    )

                with gr.Column(scale=1):
                    gr.Markdown("### 知识库操作")

                    # 新建知识库区域
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>📁 新建知识库</h4>
                        <p><strong>操作说明：</strong></p>
                        <ul>
                            <li>支持批量上传多种格式文档</li>
                            <li>系统自动提取关键词命名</li>
                            <li>支持文件夹结构上传</li>
                        </ul>
                    </div>
                    """)
                    input3 = gr.UploadButton(
                        label="📁 选择文档文件夹",
                        file_count="directory",
                        variant="primary"
                    )

                    # 删除知识库区域
                    gr.HTML("""
                    <div class="feature-card" style="margin-top: 20px;">
                        <h4>🗑️ 删除知识库</h4>
                        <p><strong>⚠️ 警告：</strong></p>
                        <ul>
                            <li>删除操作不可恢复</li>
                            <li>将删除所有相关文件和数据</li>
                            <li>请谨慎操作</li>
                        </ul>
                    </div>
                    """)

                    delete_kb_dropdown = gr.Dropdown(
                        choices=database_namelist,
                        label="选择要删除的知识库",
                        value=None,
                        info="⚠️ 请谨慎选择要删除的知识库"
                    )

                    delete_button = gr.Button(
                        "🗑️ 确认删除",
                        variant="stop"
                    )

                    delete_result = gr.Textbox(
                        label="删除结果",
                        placeholder="删除操作的结果将显示在这里...",
                        interactive=False
                    )

            knowledge_names.change(database_change, knowledge_names, context)

        with gr.Tab("🔄 训练数据生成"):
            gr.Markdown("### AI训练数据生成")

            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>功能说明</h4>
                        <p>基于知识库文档自动生成高质量问答对，用于：</p>
                        <ul>
                            <li>提升检索准确性</li>
                            <li>优化回答质量</li>
                            <li>扩充训练数据集</li>
                        </ul>
                    </div>
                    """)

                    input_qa = gr.Dropdown(
                        choices=database_namelist,
                        label="选择知识库",
                        value=database_namelist[0] if database_namelist else None,
                        info="选择要生成问答数据的知识库"
                    )

                    generate_data_button = gr.Button(
                        "🚀 开始生成训练数据",
                        variant="primary"
                    )

                with gr.Column(scale=2):
                    gr.Markdown("### 生成结果")
                    output_data = gr.DataFrame(
                        database_list[0].document.qa_df if database_list else pd.DataFrame(),
                        label="问答数据预览"
                    )

            generate_data_button.click(process_qa_data, [input_qa], [output_data])

        with gr.Tab("💬 智能问答"):
            gr.Markdown("### 企业知识问答助手")

            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>使用指南</h4>
                        <p><strong>支持的问题类型：</strong></p>
                        <ul>
                            <li>产品功能咨询</li>
                            <li>技术文档查询</li>
                            <li>流程规范说明</li>
                            <li>政策制度解读</li>
                        </ul>
                        <p><strong>提示：</strong>问题描述越具体，回答越准确</p>
                    </div>
                    """)

                    input_qa_rag = gr.Dropdown(
                        choices=database_namelist,
                        label="选择知识库",
                        value=database_namelist[0] if database_namelist else None,
                        info="选择相关的知识库进行问答"
                    )

                with gr.Column(scale=2):
                    text_input = gr.Textbox(
                        label="请输入您的问题",
                        placeholder="例如：公司的休假政策是什么？产品X的技术规格如何？",
                        lines=3
                    )

                    qa_button = gr.Button(
                        "🔍 智能问答",
                        variant="primary"
                    )

                    text_output = gr.Textbox(
                        label="AI助手回答",
                        lines=6,
                        placeholder="AI助手的回答将显示在这里..."
                    )

            with gr.Row():
                text_rag = gr.DataFrame(
                    pd.DataFrame(),
                    label="相关文档片段"
                )

            qa_button.click(generate_rag_result, [input_qa_rag, text_input], [text_output, text_rag])

        with gr.Tab("📄 内容分析"):
            gr.Markdown("### AI内容智能分析")

            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>分析功能</h4>
                        <ul>
                            <li><strong>智能摘要：</strong>提取核心要点</li>
                            <li><strong>内容分析：</strong>情感、主题、关键词</li>
                            <li><strong>自动标签：</strong>生成分类标签</li>
                        </ul>
                        <p><strong>适用场景：</strong>文档预处理、内容分类、质量评估</p>
                    </div>
                    """)

                    content_input = gr.Textbox(
                        label="输入文本内容",
                        lines=8,
                        placeholder="请粘贴需要分析的文本内容，支持中英文混合..."
                    )

                    enhance_button = gr.Button(
                        "🔍 开始分析",
                        variant="primary"
                    )

                with gr.Column(scale=1):
                    summary_output = gr.Textbox(
                        label="智能摘要",
                        lines=4,
                        placeholder="AI生成的内容摘要将显示在这里..."
                    )

                    tags_output = gr.Textbox(
                        label="自动标签",
                        placeholder="系统生成的分类标签..."
                    )

                    analysis_output = gr.JSON(
                        label="详细分析结果",
                        value={}
                    )

            def enhance_content(text):
                try:
                    if not text.strip():
                        return "请输入文本内容", {}, ""

                    # 生成摘要
                    summary = content_enhancer.generate_summary(text)

                    # 内容分析
                    analysis = content_enhancer.analyze_content(text)

                    # 生成标签
                    tags = content_enhancer.generate_content_tags(text)

                    return (
                        summary.summary_text,
                        analysis.to_dict(),
                        ", ".join(tags)
                    )
                except Exception as e:
                    return f"处理失败: {str(e)}", {"error": str(e)}, ""

            enhance_button.click(enhance_content, inputs=content_input,
                               outputs=[summary_output, analysis_output, tags_output])

        with gr.Tab("💾 数据管理"):
            gr.Markdown("### 数据导出与系统备份")

            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>数据导出</h4>
                        <p>支持多种格式导出知识库数据：</p>
                        <ul>
                            <li><strong>JSON：</strong>完整结构化数据</li>
                            <li><strong>CSV：</strong>表格数据分析</li>
                            <li><strong>Excel：</strong>业务报表格式</li>
                        </ul>
                    </div>
                    """)

                    export_kb_dropdown = gr.Dropdown(
                        choices=database_namelist,
                        label="选择知识库",
                        info="选择要导出的知识库"
                    )

                    export_format = gr.Dropdown(
                        choices=["json", "csv", "excel"],
                        value="json",
                        label="导出格式"
                    )

                    export_button = gr.Button(
                        "📤 导出数据",
                        variant="primary"
                    )

                    export_result = gr.Textbox(
                        label="导出状态",
                        placeholder="导出结果将显示在这里..."
                    )

                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="feature-card">
                        <h4>系统管理</h4>
                        <p>系统备份与统计功能：</p>
                        <ul>
                            <li><strong>自动备份：</strong>定期数据备份</li>
                            <li><strong>统计分析：</strong>使用情况统计</li>
                            <li><strong>系统监控：</strong>运行状态监控</li>
                        </ul>
                    </div>
                    """)

                    backup_button = gr.Button(
                        "🔄 创建系统备份",
                        variant="secondary"
                    )

                    backup_result = gr.Textbox(
                        label="备份状态",
                        placeholder="备份结果将显示在这里..."
                    )

                    stats_button = gr.Button(
                        "📊 查看统计",
                        variant="secondary"
                    )

                    stats_output = gr.JSON(
                        label="系统统计信息",
                        value={}
                    )

            def export_knowledge_base(kb_name, format_type):
                try:
                    if not kb_name:
                        return "请选择知识库"

                    database = database_list[database_namelist.index(kb_name)]
                    result = export_manager.export_knowledge_base(database, format_type)

                    if result["success"]:
                        return f"导出成功！文件路径: {result['output_path']}"
                    else:
                        return f"导出失败: {result['error']}"
                except Exception as e:
                    return f"导出失败: {str(e)}"

            def create_backup():
                try:
                    backup_paths = ["data/database_dir"]
                    result = export_manager.create_full_backup(backup_paths)

                    if result["success"]:
                        return f"备份成功！备份数量: {result['backup_count']}"
                    else:
                        return f"备份失败: {result['error']}"
                except Exception as e:
                    return f"备份失败: {str(e)}"

            def get_export_stats():
                try:
                    return export_manager.get_export_statistics()
                except Exception as e:
                    return {"error": str(e)}

            export_button.click(export_knowledge_base,
                              inputs=[export_kb_dropdown, export_format],
                              outputs=export_result)
            backup_button.click(create_backup, outputs=backup_result)
            stats_button.click(get_export_stats, outputs=stats_output)

        # 设置删除按钮的回调
        delete_button.click(
            delete_knowledge_base,
            inputs=[delete_kb_dropdown],
            outputs=[delete_result, knowledge_names, context, input_qa, input_qa_rag, export_kb_dropdown, delete_kb_dropdown, kb_count_display]
        )

        # 设置上传文件的回调，更新所有dropdown（包括删除dropdown和知识库数量）
        def upload_with_all_updates(files):
            result = upload(files)
            # upload函数返回5个组件，我们需要添加delete_kb_dropdown和kb_count_display的更新
            updated_choices = database_namelist
            delete_dropdown_update = gr.update(choices=updated_choices, value=None)

            # 更新知识库数量显示
            updated_count_html = f"""
            <div class="feature-card">
                <h4>当前知识库数量</h4>
                <h2 style="color: #667eea;">{len(updated_choices)}</h2>
            </div>
            """

            return result + (delete_dropdown_update, updated_count_html)

        input3.upload(upload_with_all_updates, input3, [knowledge_names, context, input_qa, input_qa_rag, export_kb_dropdown, delete_kb_dropdown, kb_count_display])

        # 添加页脚信息
        gr.HTML("""
        <div style="text-align: center; padding: 20px; margin-top: 30px; border-top: 1px solid #e9ecef; color: #6c757d;">
            <p>企业知识库管理系统 | 基于RAG技术的智能文档管理平台</p>
            <p>支持多种文档格式 • 智能问答 • 数据分析 • 安全可靠</p>
        </div>
        """)

    print("=" * 60)
    print("🚀 企业知识库管理系统启动中...")
    print("=" * 60)
    print(f"📍 访问地址: http://localhost:9999")
    print(f"👤 用户名: user")
    print(f"🔑 密码: 123")
    print(f"📚 当前知识库数量: {len(database_namelist)}")
    print("=" * 60)
    print("✅ 系统就绪，等待用户访问...")

    demo.launch(
        server_name="0.0.0.0",
        server_port=9999,
        show_api=False,
        auth=("user", "123")
    )
