import pandas as pd
from langchain_text_splitters import CharacterTextSplitter
import os
import pickle
import glob
from document_processors import DocumentProcessor, get_supported_formats
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MyDocument():
    def __init__(self, dir, name):
        self.processor = DocumentProcessor()

        if not os.path.exists(os.path.join(".cache", f"{name}_contents.pkl")):
            # 加载多种格式的文档文件
            documents = []

            # 获取所有支持的文件
            all_files = []
            supported_extensions = get_supported_formats()

            for ext in supported_extensions:
                pattern = os.path.join(dir, f"*{ext}")
                files = glob.glob(pattern)
                all_files.extend(files)

            logger.info(f"Found {len(all_files)} supported files: {all_files}")

            for file_path in all_files:
                try:
                    if self.processor.is_supported(file_path):
                        content = self.processor.extract_text(file_path)
                        if content.strip():  # 只处理非空内容
                            logger.info(f"Loaded file {file_path}, content length: {len(content)}")
                            # 创建一个简单的文档对象
                            doc = type('Document', (), {
                                'page_content': content,
                                'metadata': {
                                    'source': file_path,
                                    'file_type': self.processor.get_file_type(file_path)
                                }
                            })()
                            documents.append(doc)
                        else:
                            logger.warning(f"File {file_path} has no content")
                    else:
                        logger.warning(f"Unsupported file format: {file_path}")
                except Exception as e:
                    logger.error(f"Error loading file {file_path}: {e}")
                    continue

            logger.info(f"Total documents loaded: {len(documents)}")
            if documents:
                text_spliter = CharacterTextSplitter(chunk_size=300, chunk_overlap=50)
                split_docs = text_spliter.split_documents(documents)
                contents = [i.page_content for i in split_docs]
                logger.info(f"After splitting: {len(contents)} chunks")
            else:
                contents = []

            with open(os.path.join(".cache", f"{name}_contents.pkl"), "wb") as f:
                pickle.dump(contents, f)
        else:
            with open(os.path.join(".cache", f"{name}_contents.pkl"), "rb") as f:
                contents = pickle.load(f)
        if os.path.exists(os.path.join(".cache", f"{name}_faiss_index_df.pkl")):
            with open(os.path.join(".cache", f"{name}_faiss_index_df.pkl"), "rb") as f:
                df = pickle.load(f)
        else:
            df = pd.DataFrame({})
        self.qa_df = df
        self.contents = contents

    def get_contents(self):
        return self.contents

    def get_supported_formats(self):
        """获取支持的文件格式"""
        return get_supported_formats()

    def get_format_description(self):
        """获取支持格式的描述"""
        from document_processors import get_format_description
        return get_format_description()

if __name__ == "__main__":
    my_documents = MyDocument("data/database_dir/模型语言/txt", "模型语言")
    contents = my_documents.get_contents()
    print("knowledge num:", len(contents))
    if len(contents) > 0:
        print("knowledge demo:", contents[0])
    else:
        print("No content found!")
