from zhipuai import ZhipuAI


def get_ans(prompt):
    # 智谱api_key
    client = ZhipuAI(api_key="74659d234b004f64b70cf9bf67e77a4f.U2n59L9DzGfTe7jF")
    response = client.chat.completions.create(
        model="glm-4",
        messages=[
            {
                "role": "user",
                "content": prompt
            }
        ],
        top_p=0.3,
        temperature=0.45,
        max_tokens=1024,
        stream=True,
    )

    ans = ""
    for trunk in response:
        ans += trunk.choices[0].delta.content
    ans = ans.replace("\n\n", "\n")
    return ans


if __name__ == "__main__":
    prompt = "什么是LLM？"
    print(get_ans(prompt))
