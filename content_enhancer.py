#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容增强模块 - AI驱动的文本分析和增强功能
"""

import re
import jieba
import jieba.analyse as aly
import pandas as pd
import numpy as np
from collections import Counter
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import logging
from local_llm_model import get_ans

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入可选依赖
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available, 部分分析功能将被禁用")

@dataclass
class ContentSummary:
    """内容摘要数据类"""
    summary_text: str
    key_points: List[str]
    word_count: int
    sentence_count: int

@dataclass
class ContentAnalysis:
    """内容分析结果数据类"""
    readability_score: float
    complexity_score: float
    sentiment_score: float
    key_topics: List[str]
    language: str
    quality_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "可读性评分": self.readability_score,
            "复杂度评分": self.complexity_score,
            "情感倾向": self.sentiment_score,
            "关键主题": self.key_topics,
            "语言": self.language,
            "质量评分": self.quality_score
        }

class TextAnalyzer:
    """文本分析器"""
    
    def __init__(self):
        # 中文停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
        }
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        try:
            keywords = aly.tfidf(text, topK=top_k)
            return [kw for kw in keywords if kw not in self.stop_words]
        except Exception as e:
            logger.warning(f"关键词提取失败: {e}")
            return []
    
    def calculate_readability(self, text: str) -> float:
        """计算可读性评分"""
        try:
            # 简单的可读性评分算法
            sentences = re.split(r'[。！？]', text)
            words = list(jieba.cut(text))
            
            if len(sentences) == 0:
                return 0.0
            
            avg_sentence_length = len(words) / len(sentences)
            
            # 基于平均句长计算可读性（句子越短越易读）
            if avg_sentence_length <= 10:
                return 0.9
            elif avg_sentence_length <= 20:
                return 0.7
            elif avg_sentence_length <= 30:
                return 0.5
            else:
                return 0.3
                
        except Exception as e:
            logger.warning(f"可读性计算失败: {e}")
            return 0.5
    
    def calculate_complexity(self, text: str) -> float:
        """计算复杂度评分"""
        try:
            words = list(jieba.cut(text))
            unique_words = set(words)
            
            # 词汇多样性
            vocabulary_diversity = len(unique_words) / len(words) if words else 0
            
            # 长词比例
            long_words = [w for w in words if len(w) > 3]
            long_word_ratio = len(long_words) / len(words) if words else 0
            
            # 综合复杂度
            complexity = (vocabulary_diversity * 0.6 + long_word_ratio * 0.4)
            return min(complexity, 1.0)
            
        except Exception as e:
            logger.warning(f"复杂度计算失败: {e}")
            return 0.5
    
    def analyze_sentiment(self, text: str) -> float:
        """分析情感倾向"""
        try:
            # 简单的情感词典方法
            positive_words = {'好', '棒', '优秀', '成功', '喜欢', '满意', '高兴', '快乐', '美好', '完美'}
            negative_words = {'坏', '差', '失败', '讨厌', '不满', '难过', '痛苦', '糟糕', '可怕', '恶劣'}
            
            words = list(jieba.cut(text))
            positive_count = sum(1 for word in words if word in positive_words)
            negative_count = sum(1 for word in words if word in negative_words)
            
            total_sentiment_words = positive_count + negative_count
            if total_sentiment_words == 0:
                return 0.0  # 中性
            
            sentiment_score = (positive_count - negative_count) / total_sentiment_words
            return sentiment_score
            
        except Exception as e:
            logger.warning(f"情感分析失败: {e}")
            return 0.0

class ContentEnhancer:
    """内容增强器"""
    
    def __init__(self):
        self.analyzer = TextAnalyzer()
        if SKLEARN_AVAILABLE:
            self.vectorizer = TfidfVectorizer(max_features=1000, stop_words=list(self.analyzer.stop_words))
        else:
            self.vectorizer = None
    
    def generate_summary(self, text: str, target_length: int = 200) -> ContentSummary:
        """生成内容摘要"""
        try:
            # 使用LLM生成摘要
            prompt = f"请为以下文本生成一个简洁的摘要（约{target_length}字）：\n\n{text[:2000]}"
            summary_text = get_ans(prompt)
            
            # 提取关键点
            key_points = self._extract_key_points(text)
            
            # 统计信息
            word_count = len(list(jieba.cut(text)))
            sentence_count = len(re.split(r'[。！？]', text))
            
            return ContentSummary(
                summary_text=summary_text,
                key_points=key_points,
                word_count=word_count,
                sentence_count=sentence_count
            )
            
        except Exception as e:
            logger.error(f"摘要生成失败: {e}")
            return ContentSummary(
                summary_text="摘要生成失败",
                key_points=[],
                word_count=0,
                sentence_count=0
            )
    
    def _extract_key_points(self, text: str) -> List[str]:
        """提取关键要点"""
        try:
            sentences = re.split(r'[。！？]', text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
            
            if SKLEARN_AVAILABLE and self.vectorizer and len(sentences) > 1:
                # 使用TF-IDF选择重要句子
                sentence_vectors = self.vectorizer.fit_transform(sentences)
                sentence_scores = np.array(sentence_vectors.sum(axis=1)).flatten()
                
                # 选择得分最高的3个句子
                top_indices = sentence_scores.argsort()[-3:][::-1]
                key_points = [sentences[i] for i in sorted(top_indices)]
            else:
                # 简单选择前3个句子
                key_points = sentences[:3]
            
            return key_points
            
        except Exception as e:
            logger.warning(f"关键要点提取失败: {e}")
            return []
    
    def analyze_content(self, text: str) -> ContentAnalysis:
        """分析内容质量"""
        try:
            # 各项分析
            readability = self.analyzer.calculate_readability(text)
            complexity = self.analyzer.calculate_complexity(text)
            sentiment = self.analyzer.analyze_sentiment(text)
            key_topics = self.analyzer.extract_keywords(text, top_k=5)
            
            # 语言检测（简单判断）
            chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
            total_chars = len(text)
            language = "中文" if chinese_chars / total_chars > 0.5 else "其他"
            
            # 质量评分（综合评分）
            quality_score = (readability * 0.4 + complexity * 0.3 + 0.3)
            
            return ContentAnalysis(
                readability_score=readability,
                complexity_score=complexity,
                sentiment_score=sentiment,
                key_topics=key_topics,
                language=language,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"内容分析失败: {e}")
            return ContentAnalysis(
                readability_score=0.0,
                complexity_score=0.0,
                sentiment_score=0.0,
                key_topics=[],
                language="未知",
                quality_score=0.0
            )
    
    def generate_content_tags(self, text: str, max_tags: int = 8) -> List[str]:
        """生成内容标签"""
        try:
            # 提取关键词作为标签
            keywords = self.analyzer.extract_keywords(text, top_k=max_tags)
            
            # 如果关键词不足，使用词频统计补充
            if len(keywords) < max_tags:
                words = list(jieba.cut(text))
                word_freq = Counter(words)
                
                # 过滤停用词和短词
                filtered_words = [
                    word for word, freq in word_freq.most_common(max_tags * 2)
                    if word not in self.analyzer.stop_words and len(word) > 1
                ]
                
                # 合并关键词和高频词
                all_tags = keywords + filtered_words
                tags = list(dict.fromkeys(all_tags))[:max_tags]  # 去重并限制数量
            else:
                tags = keywords
            
            return tags
            
        except Exception as e:
            logger.error(f"标签生成失败: {e}")
            return []
    
    def calculate_similarity(self, text1: str, text2: str, method: str = "tfidf") -> float:
        """计算文本相似度"""
        try:
            if method == "tfidf" and SKLEARN_AVAILABLE and self.vectorizer:
                # 使用TF-IDF计算相似度
                vectors = self.vectorizer.fit_transform([text1, text2])
                similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
                return float(similarity)
            else:
                # 简单的词汇重叠相似度
                words1 = set(jieba.cut(text1))
                words2 = set(jieba.cut(text2))
                intersection = len(words1 & words2)
                union = len(words1 | words2)
                return intersection / union if union > 0 else 0.0
                
        except Exception as e:
            logger.warning(f"相似度计算失败: {e}")
            return 0.0
