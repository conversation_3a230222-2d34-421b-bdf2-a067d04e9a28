#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出模块 - 支持多格式导出和备份功能
"""

import os
import json
import csv
import zipfile
import shutil
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入可选依赖
try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("openpyxl not available, Excel导出功能将被禁用")

class DataExportManager:
    """数据导出管理器"""
    
    def __init__(self, export_dir: str = "data/exports", backup_dir: str = "data/backups"):
        self.export_dir = export_dir
        self.backup_dir = backup_dir
        self._ensure_directories()
        
        # 导出统计
        self.export_stats = {
            "total_exports": 0,
            "total_backups": 0,
            "last_export": None,
            "last_backup": None
        }
    
    def _ensure_directories(self):
        """确保导出和备份目录存在"""
        os.makedirs(self.export_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def export_knowledge_base(self, database, format_type: str = "json") -> Dict[str, Any]:
        """导出知识库数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            kb_name = getattr(database, 'name', 'unknown')
            
            # 准备导出数据
            export_data = {
                "knowledge_base_name": kb_name,
                "export_time": datetime.now().isoformat(),
                "document_contents": database.document.contents if hasattr(database.document, 'contents') else [],
                "qa_data": []
            }
            
            # 添加问答数据（如果存在）
            if hasattr(database.document, 'qa_df') and database.document.qa_df is not None:
                export_data["qa_data"] = database.document.qa_df.to_dict('records')
            
            # 根据格式导出
            if format_type.lower() == "json":
                return self._export_json(export_data, kb_name, timestamp)
            elif format_type.lower() == "csv":
                return self._export_csv(export_data, kb_name, timestamp)
            elif format_type.lower() == "excel" and EXCEL_AVAILABLE:
                return self._export_excel(export_data, kb_name, timestamp)
            else:
                return {"success": False, "error": f"不支持的导出格式: {format_type}"}
                
        except Exception as e:
            logger.error(f"知识库导出失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _export_json(self, data: Dict[str, Any], kb_name: str, timestamp: str) -> Dict[str, Any]:
        """导出为JSON格式"""
        try:
            filename = f"kb_{kb_name}_{timestamp}.json"
            filepath = os.path.join(self.export_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self._update_export_stats()
            logger.info(f"JSON导出成功: {filepath}")
            
            return {
                "success": True,
                "format": "json",
                "output_path": filepath,
                "file_size": os.path.getsize(filepath)
            }
            
        except Exception as e:
            return {"success": False, "error": f"JSON导出失败: {str(e)}"}
    
    def _export_csv(self, data: Dict[str, Any], kb_name: str, timestamp: str) -> Dict[str, Any]:
        """导出为CSV格式"""
        try:
            filename = f"kb_{kb_name}_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # 准备CSV数据
            csv_data = []
            
            # 添加文档内容
            for i, content in enumerate(data.get("document_contents", [])):
                csv_data.append({
                    "类型": "文档内容",
                    "序号": i + 1,
                    "内容": content,
                    "问题": "",
                    "答案": ""
                })
            
            # 添加问答数据
            for i, qa in enumerate(data.get("qa_data", [])):
                csv_data.append({
                    "类型": "问答数据",
                    "序号": i + 1,
                    "内容": "",
                    "问题": qa.get("question", ""),
                    "答案": qa.get("answer", "")
                })
            
            # 写入CSV文件
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
            else:
                # 创建空CSV文件
                with open(filepath, 'w', encoding='utf-8-sig', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(["类型", "序号", "内容", "问题", "答案"])
            
            self._update_export_stats()
            logger.info(f"CSV导出成功: {filepath}")
            
            return {
                "success": True,
                "format": "csv",
                "output_path": filepath,
                "file_size": os.path.getsize(filepath)
            }
            
        except Exception as e:
            return {"success": False, "error": f"CSV导出失败: {str(e)}"}
    
    def _export_excel(self, data: Dict[str, Any], kb_name: str, timestamp: str) -> Dict[str, Any]:
        """导出为Excel格式"""
        try:
            filename = f"kb_{kb_name}_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 导出文档内容
                if data.get("document_contents"):
                    doc_df = pd.DataFrame({
                        "序号": range(1, len(data["document_contents"]) + 1),
                        "文档内容": data["document_contents"]
                    })
                    doc_df.to_excel(writer, sheet_name='文档内容', index=False)
                
                # 导出问答数据
                if data.get("qa_data"):
                    qa_df = pd.DataFrame(data["qa_data"])
                    qa_df.to_excel(writer, sheet_name='问答数据', index=False)
                
                # 导出元数据
                meta_df = pd.DataFrame([{
                    "知识库名称": data["knowledge_base_name"],
                    "导出时间": data["export_time"],
                    "文档数量": len(data.get("document_contents", [])),
                    "问答数量": len(data.get("qa_data", []))
                }])
                meta_df.to_excel(writer, sheet_name='元数据', index=False)
            
            self._update_export_stats()
            logger.info(f"Excel导出成功: {filepath}")
            
            return {
                "success": True,
                "format": "excel",
                "output_path": filepath,
                "file_size": os.path.getsize(filepath)
            }
            
        except Exception as e:
            return {"success": False, "error": f"Excel导出失败: {str(e)}"}
    
    def create_full_backup(self, paths: List[str]) -> Dict[str, Any]:
        """创建完整系统备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"full_backup_{timestamp}.zip"
            backup_filepath = os.path.join(self.backup_dir, backup_filename)
            
            with zipfile.ZipFile(backup_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for path in paths:
                    if os.path.exists(path):
                        if os.path.isfile(path):
                            zipf.write(path, os.path.basename(path))
                        elif os.path.isdir(path):
                            for root, dirs, files in os.walk(path):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path, os.path.dirname(path))
                                    zipf.write(file_path, arcname)
            
            # 清理旧备份（保留最近10个）
            self._cleanup_old_backups()
            
            self._update_backup_stats()
            logger.info(f"系统备份成功: {backup_filepath}")
            
            return {
                "success": True,
                "backup_path": backup_filepath,
                "backup_size": os.path.getsize(backup_filepath),
                "backup_count": len(os.listdir(self.backup_dir))
            }
            
        except Exception as e:
            logger.error(f"系统备份失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _cleanup_old_backups(self, max_backups: int = 10):
        """清理旧备份文件"""
        try:
            backup_files = [
                f for f in os.listdir(self.backup_dir)
                if f.startswith("full_backup_") and f.endswith(".zip")
            ]
            
            if len(backup_files) > max_backups:
                # 按修改时间排序
                backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)))
                
                # 删除最旧的备份
                for old_backup in backup_files[:-max_backups]:
                    old_path = os.path.join(self.backup_dir, old_backup)
                    os.remove(old_path)
                    logger.info(f"删除旧备份: {old_backup}")
                    
        except Exception as e:
            logger.warning(f"清理旧备份失败: {e}")
    
    def _update_export_stats(self):
        """更新导出统计"""
        self.export_stats["total_exports"] += 1
        self.export_stats["last_export"] = datetime.now().isoformat()
    
    def _update_backup_stats(self):
        """更新备份统计"""
        self.export_stats["total_backups"] += 1
        self.export_stats["last_backup"] = datetime.now().isoformat()
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """获取导出统计信息"""
        try:
            # 统计导出文件
            export_files = os.listdir(self.export_dir) if os.path.exists(self.export_dir) else []
            backup_files = os.listdir(self.backup_dir) if os.path.exists(self.backup_dir) else []
            
            # 计算总大小
            export_size = sum(
                os.path.getsize(os.path.join(self.export_dir, f))
                for f in export_files
                if os.path.isfile(os.path.join(self.export_dir, f))
            )
            
            backup_size = sum(
                os.path.getsize(os.path.join(self.backup_dir, f))
                for f in backup_files
                if os.path.isfile(os.path.join(self.backup_dir, f))
            )
            
            return {
                "导出文件数量": len(export_files),
                "备份文件数量": len(backup_files),
                "导出文件总大小(MB)": round(export_size / (1024 * 1024), 2),
                "备份文件总大小(MB)": round(backup_size / (1024 * 1024), 2),
                "最后导出时间": self.export_stats.get("last_export", "无"),
                "最后备份时间": self.export_stats.get("last_backup", "无"),
                "总导出次数": self.export_stats.get("total_exports", 0),
                "总备份次数": self.export_stats.get("total_backups", 0)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def restore_backup(self, backup_filename: str, restore_path: str = ".") -> Dict[str, Any]:
        """恢复备份"""
        try:
            backup_filepath = os.path.join(self.backup_dir, backup_filename)
            
            if not os.path.exists(backup_filepath):
                return {"success": False, "error": "备份文件不存在"}
            
            with zipfile.ZipFile(backup_filepath, 'r') as zipf:
                zipf.extractall(restore_path)
            
            logger.info(f"备份恢复成功: {backup_filename}")
            
            return {
                "success": True,
                "restored_from": backup_filepath,
                "restored_to": restore_path
            }
            
        except Exception as e:
            logger.error(f"备份恢复失败: {e}")
            return {"success": False, "error": str(e)}
