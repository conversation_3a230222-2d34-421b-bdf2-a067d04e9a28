from pypinyin import lazy_pinyin
import pickle
import os
import faiss
import pandas as pd
from emb_model import MyEmbModel
from load_documents import MyDocument


def cover_name(name):
    return "_".join(lazy_pinyin(name))


class MyEmbDatabase():
    def __init__(self, emb_dir, qa_df, name):
        global client
        self.name = cover_name(name)
        self.emb_model = MyEmbModel(emb_dir)

        # 处理qa_df为None或空的情况
        if qa_df is None or qa_df.empty:
            # 创建空的索引和DataFrame
            index = faiss.IndexFlatL2(self.emb_model.model.get_sentence_embedding_dimension())
            qa_df = pd.DataFrame(columns=["question", "answer"])
        elif not os.path.exists(os.path.join(".cache", f"{name}_faiss_index.pkl")):
            # 检查qa_df是否包含必要的列
            if "question" not in qa_df.columns:
                raise ValueError(f"qa_df必须包含'question'列，当前列: {qa_df.columns.tolist()}")

            index = faiss.IndexFlatL2(self.emb_model.model.get_sentence_embedding_dimension())
            if len(qa_df) > 0:
                embs = self.emb_model.to_emb(qa_df["question"].tolist())
                index.add(embs)

            with open(os.path.join(".cache", f"{name}_faiss_index.pkl"), "wb") as f:
                pickle.dump(index, f)
            with open(os.path.join(".cache", f"{name}_faiss_index_df.pkl"), "wb") as f:
                pickle.dump(qa_df, f)
        else:
            with open(os.path.join(".cache", f"{name}_faiss_index.pkl"), "rb") as f:
                index = pickle.load(f)
            with open(os.path.join(".cache", f"{name}_faiss_index_df.pkl"), "rb") as f:
                qa_df = pickle.load(f)

        self.index = index
        self.qa_df = qa_df

    def search(self, content, topn=3):
        global client

        # 检查是否有数据可搜索
        if self.qa_df.empty or len(self.qa_df) == 0:
            # 返回空的DataFrame
            return pd.DataFrame(columns=["question", "answer"])

        if isinstance(content, str):
            content = self.emb_model.to_emb(content)

        try:
            distances, idxs = self.index.search(content, topn)
            # 确保索引不超出范围
            valid_idxs = [idx for idx in idxs[0] if 0 <= idx < len(self.qa_df)]
            if valid_idxs:
                results = self.qa_df.iloc[valid_idxs]
            else:
                results = pd.DataFrame(columns=["question", "answer"])
            return results
        except Exception as e:
            print(f"搜索时出错: {e}")
            return pd.DataFrame(columns=["question", "answer"])


class MyDataBase:
    def __init__(self, path, name=None):
        if name is None:
            name = path
        print(os.path.join(path, "txt"))
        if not os.path.exists(os.path.join(path, "txt")):
            print("对应的知识库txt文件夹不存在")
            exit(-999)

        if not os.path.exists(os.path.join(path, "qa")):
            print("对应的知识库没有进行QA问答对抽取")
            exit(-999)

        if not os.path.exists(".cache"):
            os.mkdir(".cache")
        self.name = name
        self.document = MyDocument(os.path.join(path, "txt"), name)
        self.emb_database = None

    def create_emb_database(self, qa_df):
        self.emb_database = MyEmbDatabase("moka-ai_m3e-base", qa_df, self.name)
        self.qa_df = qa_df

    def search(self, text, topn=3):
        if not self.emb_database:
            self.emb_database = MyEmbDatabase("moka-ai_m3e-base", None, self.name)
        return self.emb_database.search(text, topn)


def load_database(dir_path="data/database_dir"):
    dirs = [name for name in os.listdir(dir_path) if os.path.isdir(f"{dir_path}/{name}")]

    database_list = []
    database_namelist = []

    for dir in dirs:
        database = MyDataBase(f"{dir_path}/{dir}",dir)

        database_list.append(database)
        database_namelist.append(dir)

    return database_list,database_namelist


if __name__ == "__main__":
    # 测试文档加载
    print("=== 测试模型语言知识库 ===")
    database = MyDataBase("data/database_dir/模型语言", "模型语言")
    print(f"文档内容数量: {len(database.document.contents)}")

    if len(database.document.contents) > 0:
        print("文档内容示例:")
        print(database.document.contents[0][:200] + "...")

        # 创建一个简单的问答对用于测试
        import pandas as pd
        test_qa = pd.DataFrame({
            'question': ['什么是Transformer', '什么是大模型', 'GPT是什么'],
            'answer': [database.document.contents[0][:300],
                      database.document.contents[0][:300],
                      database.document.contents[0][:300]]
        })

        database.create_emb_database(test_qa)
        result = database.search("什么是Transformer")
        print("\n搜索结果:")
        print(result)
    else:
        print("没有找到文档内容")
