#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多格式文档处理器模块
支持PDF、DOCX、MD、XLSX、PPTX等格式的文本提取
"""

import os
import logging
from typing import List, Dict, Optional, Union
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 支持的文件格式
SUPPORTED_FORMATS = {
    '.txt': 'text',
    '.pdf': 'pdf', 
    '.docx': 'docx',
    '.md': 'markdown',
    '.markdown': 'markdown',
    '.xlsx': 'excel',
    '.xls': 'excel',
    '.pptx': 'powerpoint',
    '.ppt': 'powerpoint'
}


class DocumentProcessor:
    """文档处理器基类"""
    
    def __init__(self):
        self.supported_formats = SUPPORTED_FORMATS
    
    def get_file_type(self, file_path: str) -> Optional[str]:
        """获取文件类型"""
        ext = Path(file_path).suffix.lower()
        return self.supported_formats.get(ext)
    
    def is_supported(self, file_path: str) -> bool:
        """检查文件是否支持"""
        return self.get_file_type(file_path) is not None
    
    def extract_text(self, file_path: str) -> str:
        """提取文档文本内容"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_type = self.get_file_type(file_path)
        if not file_type:
            raise ValueError(f"不支持的文件格式: {file_path}")
        
        try:
            if file_type == 'text':
                return self._extract_text_from_txt(file_path)
            elif file_type == 'pdf':
                return self._extract_text_from_pdf(file_path)
            elif file_type == 'docx':
                return self._extract_text_from_docx(file_path)
            elif file_type == 'markdown':
                return self._extract_text_from_markdown(file_path)
            elif file_type == 'excel':
                return self._extract_text_from_excel(file_path)
            elif file_type == 'powerpoint':
                return self._extract_text_from_pptx(file_path)
            else:
                raise ValueError(f"未实现的文件类型处理: {file_type}")
        except Exception as e:
            logger.error(f"提取文本失败 {file_path}: {e}")
            raise
    
    def _extract_text_from_txt(self, file_path: str) -> str:
        """提取TXT文件文本"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"无法解码文件: {file_path}")
    
    def _extract_text_from_pdf(self, file_path: str) -> str:
        """提取PDF文件文本"""
        try:
            import PyPDF2
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except ImportError:
            raise ImportError("请安装PyPDF2库: pip install PyPDF2")
        except Exception as e:
            logger.warning(f"PyPDF2提取失败，尝试备用方法: {e}")
            # 可以在这里添加其他PDF处理库作为备用
            return ""
    
    def _extract_text_from_docx(self, file_path: str) -> str:
        """提取DOCX文件文本"""
        try:
            from docx import Document
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            return text.strip()
        except ImportError:
            raise ImportError("请安装python-docx库: pip install python-docx")
    
    def _extract_text_from_markdown(self, file_path: str) -> str:
        """提取Markdown文件文本"""
        try:
            import markdown
            from markdown.extensions import codehilite
            
            with open(file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # 转换为HTML然后提取纯文本
            md = markdown.Markdown(extensions=['codehilite'])
            html = md.convert(md_content)
            
            # 简单的HTML标签移除
            import re
            text = re.sub(r'<[^>]+>', '', html)
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
        except ImportError:
            # 如果没有markdown库，直接读取原始内容
            return self._extract_text_from_txt(file_path)
    
    def _extract_text_from_excel(self, file_path: str) -> str:
        """提取Excel文件文本"""
        try:
            import openpyxl
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text = ""
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"工作表: {sheet_name}\n"
                
                for row in sheet.iter_rows(values_only=True):
                    row_text = []
                    for cell in row:
                        if cell is not None:
                            row_text.append(str(cell))
                    if row_text:
                        text += " | ".join(row_text) + "\n"
                text += "\n"
            
            return text.strip()
        except ImportError:
            raise ImportError("请安装openpyxl库: pip install openpyxl")
    
    def _extract_text_from_pptx(self, file_path: str) -> str:
        """提取PowerPoint文件文本"""
        try:
            from pptx import Presentation
            prs = Presentation(file_path)
            text = ""
            
            for i, slide in enumerate(prs.slides, 1):
                text += f"幻灯片 {i}:\n"
                
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
                
                text += "\n"
            
            return text.strip()
        except ImportError:
            raise ImportError("请安装python-pptx库: pip install python-pptx")


def get_supported_formats() -> List[str]:
    """获取支持的文件格式列表"""
    return list(SUPPORTED_FORMATS.keys())


def get_format_description() -> str:
    """获取支持格式的描述"""
    formats = {
        'text': 'TXT文本文件',
        'pdf': 'PDF文档',
        'docx': 'Word文档',
        'markdown': 'Markdown文档',
        'excel': 'Excel表格',
        'powerpoint': 'PowerPoint演示文稿'
    }
    
    result = "支持的文件格式:\n"
    for ext, file_type in SUPPORTED_FORMATS.items():
        result += f"  {ext} - {formats.get(file_type, file_type)}\n"
    
    return result


if __name__ == "__main__":
    # 测试文档处理器
    processor = DocumentProcessor()
    print(get_format_description())
    
    # 测试支持的格式检查
    test_files = ["test.txt", "test.pdf", "test.docx", "test.md", "test.xlsx", "test.pptx", "test.unknown"]
    for file in test_files:
        print(f"{file}: {'支持' if processor.is_supported(file) else '不支持'}")
